<project name="StudentDetailsProject" default="run" basedir=".">

  <!-- Define properties -->
  <property name="src.dir" value="src"/>
  <property name="build.dir" value="build"/>
  <property name="lib.dir" value="lib"/>

  <!-- Define classpath -->
  <path id="classpath">
    <fileset dir="${lib.dir}">
      <include name="*.jar"/>
    </fileset>
    <pathelement location="${build.dir}"/>
  </path>

  <!-- Clean target -->
  <target name="clean" description="Clean build directory">
    <delete dir="${build.dir}"/>
    <echo message="Build directory cleaned"/>
  </target>

  <!-- Compile target -->
  <target name="compile" depends="clean" description="Compile Java source files">
    <mkdir dir="${build.dir}"/>
    <javac srcdir="${src.dir}"
           destdir="${build.dir}"
           classpathref="classpath"
           includeantruntime="false">
      <compilerarg value="-Xlint:unchecked"/>
    </javac>
    <echo message="Compilation completed successfully"/>
  </target>

  <!-- Run target to get student details -->
  <target name="run" depends="compile" description="Run the application to get student details">
    <echo message="Running Student Details Application..."/>
    <echo message="========================================"/>
    <java classname="jdbc.example1.Main"
          classpathref="classpath"
          fork="true">
    </java>
    <echo message="========================================"/>
    <echo message="Student details retrieval completed"/>
  </target>

  <!-- Alternative target to run without recompiling -->
  <target name="run-only" description="Run the application without recompiling">
    <echo message="Running Student Details Application (no recompile)..."/>
    <echo message="========================================"/>
    <java classname="jdbc.example1.Main"
          classpathref="classpath"
          fork="true">
    </java>
    <echo message="========================================"/>
  </target>

  <!-- DB operation targets using DBTool -->
  <target name="select" depends="compile" description="List all students">
    <echo message="Selecting all students..."/>
    <java classname="jdbc.example1.DBTool" classpathref="classpath" fork="true">
      <arg value="select"/>
    </java>
  </target>

  <target name="insert" depends="compile" description="Insert a student (set id,name,email properties)">
    <echo message="Inserting a student id=${id} name=${name} email=${email}"/>
    <java classname="jdbc.example1.DBTool" classpathref="classpath" fork="true">
      <arg value="insert"/>
      <arg value="${id}"/>
      <arg value="${name}"/>
      <arg value="${email}"/>
    </java>
  </target>

  <target name="update" depends="compile" description="Update a student (set id,name,email properties)">
    <echo message="Updating student id=${id} name=${name} email=${email}"/>
    <java classname="jdbc.example1.DBTool" classpathref="classpath" fork="true">
      <arg value="update"/>
      <arg value="${id}"/>
      <arg value="${name}"/>
      <arg value="${email}"/>
    </java>
  </target>

  <target name="delete" depends="compile" description="Delete a student (set id property)">
    <echo message="Deleting student id=${id}"/>
    <java classname="jdbc.example1.DBTool" classpathref="classpath" fork="true">
      <arg value="delete"/>
      <arg value="${id}"/>
    </java>
  </target>

</project>
