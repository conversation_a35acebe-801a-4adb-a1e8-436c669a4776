package jdbc.example1;

import java.sql.*;

public class DBTool {
    private static final String URL = "******************************************";
    private static final String USER = "root";
    private static final String PASS = "Gommala";

    public static void main(String[] args) {
        if (args.length == 0) {
            System.out.println("Usage: select | insert <id> <name> <email> | update <id> <name> <email> | delete <id>");
            return;
        }

        String cmd = args[0];
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            System.err.println("MySQL JDBC driver not found. Put Connector/J in lib/ and update build.xml classpath.");
            return;
        }

        try (Connection con = DriverManager.getConnection(URL, USER, PASS)) {
            switch (cmd) {
                case "select":
                    doSelect(con);
                    break;
                case "init":
                    createTableIfNotExists(con);
                    break;
                case "insert":
                    if (args.length < 4) {
                        System.out.println("insert requires id name email");
                        return;
                    }
                    doInsert(con, Integer.parseInt(args[1]), args[2], args[3]);
                    break;
                case "update":
                    if (args.length < 4) {
                        System.out.println("update requires id name email");
                        return;
                    }
                    doUpdate(con, Integer.parseInt(args[1]), args[2], args[3]);
                    break;
                case "delete":
                    if (args.length < 2) {
                        System.out.println("delete requires id");
                        return;
                    }
                    doDelete(con, Integer.parseInt(args[1]));
                    break;
                default:
                    System.out.println("Unknown command: " + cmd);
            }
        } catch (SQLException e) {
            System.err.println("SQL error:");
            e.printStackTrace();
        }
    }

    private static void createTableIfNotExists(Connection con) throws SQLException {
        String ddl = "CREATE TABLE IF NOT EXISTS students ("
                + "id INT PRIMARY KEY, "
                + "name VARCHAR(100), "
                + "email VARCHAR(255)"
                + ") ENGINE=InnoDB";
        try (Statement st = con.createStatement()) {
            st.execute(ddl);
            System.out.println("Table 'students' ensured (created if missing)");
        }
    }

    private static void doSelect(Connection con) throws SQLException {
        String q = "SELECT * FROM students";
        try (Statement st = con.createStatement(); ResultSet rs = st.executeQuery(q)) {
            ResultSetMetaData md = rs.getMetaData();
            int cols = md.getColumnCount();
            while (rs.next()) {
                StringBuilder sb = new StringBuilder();
                for (int i = 1; i <= cols; i++) {
                    if (i > 1) sb.append(" | ");
                    sb.append(md.getColumnLabel(i)).append(": ").append(rs.getObject(i));
                }
                System.out.println(sb.toString());
            }
        }
    }

    private static void doInsert(Connection con, int id, String name, String email) throws SQLException {
        String q = "INSERT INTO students (id, name, email) VALUES (?, ?, ?)";
        try (PreparedStatement ps = con.prepareStatement(q)) {
            ps.setInt(1, id);
            ps.setString(2, name);
            ps.setString(3, email);
            int n = ps.executeUpdate();
            System.out.println("Inserted " + n + " row(s)");
        }
    }

    private static void doUpdate(Connection con, int id, String name, String email) throws SQLException {
        String q = "UPDATE students SET name = ?, email = ? WHERE id = ?";
        try (PreparedStatement ps = con.prepareStatement(q)) {
            ps.setString(1, name);
            ps.setString(2, email);
            ps.setInt(3, id);
            int n = ps.executeUpdate();
            System.out.println("Updated " + n + " row(s)");
        }
    }

    private static void doDelete(Connection con, int id) throws SQLException {
        String q = "DELETE FROM students WHERE id = ?";
        try (PreparedStatement ps = con.prepareStatement(q)) {
            ps.setInt(1, id);
            int n = ps.executeUpdate();
            System.out.println("Deleted " + n + " row(s)");
        }
    }
}
