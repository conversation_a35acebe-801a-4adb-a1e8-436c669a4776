package jdbc.example1;

import java.sql.*;

public class Main {
    public static void main(String[] args) {
        String url = "******************************************";
        String user = "root"; 
        String password = "Gommala"; 

        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection con = DriverManager.getConnection(url, user, password);
            System.out.println("✅ Connected to MySQL successfully!");

            Statement stmt = con.createStatement();
            ResultSet rs = stmt.executeQuery("SELECT * FROM student");

            while (rs.next()) {
                System.out.println(rs.getInt("id") + " - " + rs.getString("name") + " - " + rs.getString("email"));
            }

            con.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}